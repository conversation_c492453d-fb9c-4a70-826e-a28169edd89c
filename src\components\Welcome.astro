---
// Professional QR Analytics Platform Landing Page
---

<div class="min-h-screen bg-white">
	<!-- Hero Section -->
	<section class="relative py-24 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
		<!-- Background Pattern -->
		<div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

		<div class="relative max-w-7xl mx-auto px-6">
			<div class="text-center max-w-4xl mx-auto">
				<!-- Badge -->
				<div class="mb-8">
					<span class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-700 text-sm font-medium border border-blue-200">
						🚀 Professional QR Analytics Platform
					</span>
				</div>

				<!-- Main Headline -->
				<h1 class="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
					Transform Your
					<span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">QR Campaigns</span>
					with Analytics
				</h1>

				<!-- Subtitle -->
				<p class="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
					Create, track, and optimize QR codes with enterprise-grade analytics.
					Make data-driven decisions and maximize your campaign ROI.
				</p>

				<!-- Value Props -->
				<div class="grid md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
					<div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-sm">
						<div class="text-blue-600 text-2xl mb-3">📊</div>
						<h3 class="font-semibold text-gray-900 mb-2">Real-Time Analytics</h3>
						<p class="text-gray-600 text-sm">Track every scan with detailed insights and performance metrics</p>
					</div>
					<div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-sm">
						<div class="text-purple-600 text-2xl mb-3">🌍</div>
						<h3 class="font-semibold text-gray-900 mb-2">Global Intelligence</h3>
						<p class="text-gray-600 text-sm">Geographic and demographic insights from around the world</p>
					</div>
					<div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-sm">
						<div class="text-green-600 text-2xl mb-3">⚡</div>
						<h3 class="font-semibold text-gray-900 mb-2">Dynamic Management</h3>
						<p class="text-gray-600 text-sm">Update destinations instantly without regenerating codes</p>
					</div>
				</div>

				<!-- CTA Buttons -->
				<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
					<a href="/tool/qr-code-generator"
						class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
						<span>Start Creating QR Codes</span>
						<svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
						</svg>
					</a>
					<!-- <a href="/dashboard"
						class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-xl border border-gray-200 hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-md">
						<span>View Demo Dashboard</span>
					</a> -->
				</div>

				<p class="text-gray-500 text-sm mt-6">
					✨ Free to start • No credit card required • Professional analytics included
				</p>
			</div>
		</div>
	</section>

	<!-- Problem & Solution -->
	<section class="py-20 bg-gray-50">
		<div class="max-w-6xl mx-auto px-6">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
					Why QR Analytics Matter
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Traditional QR codes leave you in the dark. Our platform provides the insights you need to optimize campaigns and drive results.
				</p>
			</div>

			<div class="grid lg:grid-cols-2 gap-12 items-center">
				<!-- Problem Side -->
				<div class="space-y-8">
					<div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
						<div class="flex items-start space-x-4">
							<div class="flex-shrink-0 w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
								<svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-lg font-semibold text-gray-900 mb-2">Limited Campaign Insights</h3>
								<p class="text-gray-600">Without analytics, you can't measure campaign effectiveness or identify optimization opportunities.</p>
							</div>
						</div>
					</div>

					<div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
						<div class="flex items-start space-x-4">
							<div class="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
								<svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-lg font-semibold text-gray-900 mb-2">Missed Opportunities</h3>
								<p class="text-gray-600">Competitors with data-driven insights consistently outperform campaigns without proper tracking.</p>
							</div>
						</div>
					</div>

					<div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
						<div class="flex items-start space-x-4">
							<div class="flex-shrink-0 w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
								<svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-lg font-semibold text-gray-900 mb-2">Resource Inefficiency</h3>
								<p class="text-gray-600">Marketing budgets are wasted on campaigns that can't be measured or optimized for better performance.</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Solution Side -->
				<div class="bg-gradient-to-br from-blue-600 to-purple-600 rounded-3xl p-8 text-white">
					<div class="text-center mb-8">
						<div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
							</svg>
						</div>
						<h3 class="text-2xl font-bold mb-4">QRAnalytica Solution</h3>
						<p class="text-blue-100">Transform your QR campaigns with comprehensive analytics and insights</p>
					</div>

					<div class="space-y-6">
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
								<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span class="text-blue-100">Real-time scan tracking and analytics</span>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
								<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span class="text-blue-100">Geographic and demographic insights</span>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
								<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span class="text-blue-100">Dynamic URL management</span>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
								<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span class="text-blue-100">Professional reporting and exports</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Features Section -->
	<section id="features" class="py-20 bg-white">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
					Powerful Features for
					<span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Professional QR Analytics</span>
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Everything you need to create, track, and optimize QR code campaigns with enterprise-grade analytics.
				</p>
			</div>

			<div class="grid lg:grid-cols-2 gap-8 mb-16">
				<!-- Real-Time Analytics -->
				<div class="group bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100 hover:shadow-lg transition-all duration-300">
					<div class="flex items-center mb-6">
						<div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white text-2xl mr-4 group-hover:scale-110 transition-transform duration-300">
							📊
						</div>
						<h3 class="text-2xl font-bold text-gray-900">Real-Time Analytics</h3>
					</div>
					<p class="text-gray-600 mb-6 leading-relaxed">
						Monitor your QR code performance with live tracking, detailed metrics, and comprehensive insights into user behavior.
					</p>
					<div class="space-y-3">
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Live scan tracking and visitor metrics</span>
						</div>
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Time-based analysis and patterns</span>
						</div>
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Performance benchmarking</span>
						</div>
					</div>
				</div>

				<!-- Geographic Intelligence -->
				<div class="group bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
					<div class="flex items-center mb-6">
						<div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white text-2xl mr-4 group-hover:scale-110 transition-transform duration-300">
							�
						</div>
						<h3 class="text-2xl font-bold text-gray-900">Geographic Intelligence</h3>
					</div>
					<p class="text-gray-600 mb-6 leading-relaxed">
						Understand your global reach with detailed geographic analytics, from countries down to city-level insights.
					</p>
					<div class="space-y-3">
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Country and city-level tracking</span>
						</div>
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Timezone analysis and optimization</span>
						</div>
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Regional performance insights</span>
						</div>
					</div>
				</div>

				<!-- Device Analytics -->
				<div class="group bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100 hover:shadow-lg transition-all duration-300">
					<div class="flex items-center mb-6">
						<div class="w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white text-2xl mr-4 group-hover:scale-110 transition-transform duration-300">
							📱
						</div>
						<h3 class="text-2xl font-bold text-gray-900">Device & System Analytics</h3>
					</div>
					<p class="text-gray-600 mb-6 leading-relaxed">
						Get detailed insights into how users interact with your QR codes across different devices and platforms.
					</p>
					<div class="space-y-3">
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Device type and model tracking</span>
						</div>
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Operating system analysis</span>
						</div>
						<!-- <div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Browser and app insights</span>
						</div> -->
					</div>
				</div>

				<!-- Professional Management -->
				<div class="group bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-8 border border-orange-100 hover:shadow-lg transition-all duration-300">
					<div class="flex items-center mb-6">
						<div class="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white text-2xl mr-4 group-hover:scale-110 transition-transform duration-300">
							⚡
						</div>
						<h3 class="text-2xl font-bold text-gray-900">Dynamic Management</h3>
					</div>
					<p class="text-gray-600 mb-6 leading-relaxed">
						Professional QR code management with dynamic URLs, custom domains, and comprehensive reporting tools.
					</p>
					<div class="space-y-3">
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Dynamic URL updates</span>
						</div>
						<div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Custom domain integration</span>
						</div>
						<!-- <div class="flex items-center text-gray-700">
							<div class="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
								<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<span>Excel export(Coming Soon)</span>
						</div> -->
					</div>
				</div>
			</div>

			<!-- CTA Section -->
			<div class="text-center">
				<div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 max-w-3xl mx-auto shadow-xl">
					<h3 class="text-3xl font-bold text-white mb-4">Ready to Transform Your QR Campaigns?</h3>
					<p class="text-blue-100 mb-8 text-lg">
						Join thousands of professionals who trust QRAnalytica for their QR code analytics needs.
					</p>
					<div class="flex flex-col sm:flex-row gap-4 justify-center">
						<a href="/tool/qr-code-generator"
							class="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl">
							<span>Start Creating QR Codes</span>
							<svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
							</svg>
						</a>
						<a href="/dashboard"
							class="inline-flex items-center px-8 py-4 bg-blue-500 text-white font-semibold rounded-xl hover:bg-blue-400 transition-all duration-300 border-2 border-blue-400">
							<span>View Analytics Demo</span>
						</a>
					</div>
					<p class="text-blue-200 text-sm mt-6">
						✨ Free to start • Professional analytics included • No setup fees
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Social Proof & Testimonials -->
	<section class="py-20 bg-gray-50">
		<div class="max-w-6xl mx-auto px-6">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
					Trusted by Professionals Worldwide
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Join thousands of marketers, agencies, and businesses who rely on QRAnalytica for their QR code campaigns.
				</p>
			</div>

			<!-- Stats -->
			<div class="grid md:grid-cols-4 gap-8 mb-16">
				<div class="text-center">
					<div class="text-4xl font-bold text-blue-600 mb-2">10K+</div>
					<div class="text-gray-600">QR Codes Created</div>
				</div>
				<div class="text-center">
					<div class="text-4xl font-bold text-purple-600 mb-2">500K+</div>
					<div class="text-gray-600">Scans Tracked</div>
				</div>
				<div class="text-center">
					<div class="text-4xl font-bold text-green-600 mb-2">150+</div>
					<div class="text-gray-600">Countries Reached</div>
				</div>
				<div class="text-center">
					<div class="text-4xl font-bold text-orange-600 mb-2">98%</div>
					<div class="text-gray-600">Customer Satisfaction</div>
				</div>
			</div>

			<!-- Testimonials -->
			<div class="grid md:grid-cols-3 gap-8">
				<div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
					<div class="flex items-center mb-4">
						<div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
							<span class="text-blue-600 font-bold text-lg">M</span>
						</div>
						<div>
							<div class="font-semibold text-gray-900">Marketing Director</div>
							<div class="text-gray-600 text-sm">Tech Startup</div>
						</div>
					</div>
					<p class="text-gray-700 leading-relaxed">
						"QRAnalytica transformed our campaign tracking. The geographic insights helped us identify our strongest markets and optimize our budget allocation."
					</p>
					<div class="flex text-yellow-400 mt-4">
						★★★★★
					</div>
				</div>

				<div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
					<div class="flex items-center mb-4">
						<div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
							<span class="text-purple-600 font-bold text-lg">S</span>
						</div>
						<div>
							<div class="font-semibold text-gray-900">Agency Owner</div>
							<div class="text-gray-600 text-sm">Digital Marketing</div>
						</div>
					</div>
					<p class="text-gray-700 leading-relaxed">
						"The real-time analytics and professional reporting features have made client presentations so much more impactful. ROI tracking is now effortless."
					</p>
					<div class="flex text-yellow-400 mt-4">
						★★★★★
					</div>
				</div>

				<div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
					<div class="flex items-center mb-4">
						<div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
							<span class="text-green-600 font-bold text-lg">R</span>
						</div>
						<div>
							<div class="font-semibold text-gray-900">Restaurant Manager</div>
							<div class="text-gray-600 text-sm">Chain Restaurant</div>
						</div>
					</div>
					<p class="text-gray-700 leading-relaxed">
						"Dynamic URL management is a game-changer. We can update our menu links instantly without reprinting QR codes. Saved us thousands in printing costs."
					</p>
					<div class="flex text-yellow-400 mt-4">
						★★★★★
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Transparent Pricing -->
	<section id="pricing" class="py-20 bg-white">
		<div class="max-w-7xl mx-auto px-6 text-center">
			<div class="mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
					Transparent Pricing
				</h2>
				<p class="text-xl text-gray-600 max-w-2xl mx-auto">
					Simple, transparent pricing. No surprises.
				</p>
			</div>

			<div class="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
				<!-- Essential Plan -->
				<div class="bg-gray-50 rounded-2xl p-8 border border-gray-200">
					<div class="text-center mb-8">
						<h3 class="text-2xl font-bold text-gray-900 mb-2">Essential</h3>
						<p class="text-gray-600 mb-6">Perfect for basic QR code needs</p>
						<div class="text-4xl font-bold text-gray-900 mb-2">$0</div>
						<p class="text-gray-600">forever</p>
					</div>
					<ul class="space-y-4 mb-8 text-left">
						<li class="flex items-center text-gray-700">
							<svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
							</svg>
							<span>Static QR Codes (No Login)</span>
						</li>
						<li class="flex items-center text-gray-700">
							<svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
							</svg>
							<span>Basic QR Types</span>
						</li>
						<li class="flex items-center text-gray-700">
							<svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
							</svg>
							<span>Standard Quality</span>
						</li>
						<li class="flex items-center text-gray-400">
							<svg class="w-5 h-5 text-gray-300 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
							</svg>
							<span>Analytics</span>
						</li>
						<li class="flex items-center text-gray-400">
							<svg class="w-5 h-5 text-gray-300 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
							</svg>
							<span>Custom Domain</span>
						</li>
					</ul>
					<a href="/tool/qr-code-generator"
						class="block w-full bg-gray-200 text-gray-800 py-3 rounded-xl font-semibold hover:bg-gray-300 transition-colors">
						Get Started Free
					</a>
				</div>

				<!-- Professional Plan -->
				<div class="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-8 text-white relative transform scale-105">
					<div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
						<span class="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium">
							Popular
						</span>
					</div>
					<div class="text-center mb-8">
						<h3 class="text-2xl font-bold mb-2">Professional</h3>
						<p class="text-blue-100 mb-6">Complete QR analytics suite</p>

						<div class="bg-white/10 rounded-lg p-4 mb-4">
							<div class="text-sm text-blue-200 mb-2">7 Days Free Trial</div>
							<div class="flex items-center justify-center gap-2 mb-2">
								<span class="text-sm text-blue-200">No credit card required</span>
							</div>
							<div class="text-3xl font-bold text-white">$0</div>
							<div class="text-blue-100">for 7 days</div>
						</div>

						<p class="text-xs text-blue-200">
							One-time payment of $99 after 7-day trial. No recurring fees or hidden charges.
						</p>
					</div>

					<div class="mb-8">
						<h4 class="text-lg font-semibold mb-4 text-center">Everything you get:</h4>
						<ul class="space-y-3 text-left text-sm">
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Everything in Essential</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Unlimited Dynamic QRs</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Total Scans and Unique Users</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Scans by Time of day</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Device & OS Analytics</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Geographic Insights</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Dynamic URL Updates for QR Codes (Up to 5 Changes)</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Unlimited Scans(Upto 1month history)</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>7 Days Free Trial - Test Everything</span>
							</li>
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>LifeTime Updates</span>
							</li>
							<!-- 1 custom domain -->
							<li class="flex items-center">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>1 Custom Domain</span>
							</li>
							<!-- 1 custom domain -->
						</ul>
					</div>

					<a href="/tool/qr-code-generator"
						class="block w-full bg-white text-blue-600 py-3 rounded-xl font-semibold hover:bg-gray-50 transition-colors">
						Start 7-Day Free Trial
					</a>
				</div>

				<!-- Enterprise Plan -->
				<div class="bg-gray-900 rounded-2xl p-8 border border-gray-700 text-white">
					<div class="text-center mb-8">
						<h3 class="text-2xl font-bold text-white mb-2">Enterprise</h3>
						<p class="text-gray-300 mb-6">Tailored solutions for large organizations</p>
						<div class="text-4xl font-bold text-white mb-2">Custom</div>
						<p class="text-gray-300">solution</p>
					</div>

					<div class="mb-8">
						<h4 class="text-lg font-semibold mb-4 text-center">Let's Build Your Perfect Solution</h4>
						<p class="text-gray-300 text-sm mb-6 text-center">
							Get a customized plan that scales with your needs. Our enterprise solutions offer:
						</p>
						<ul class="space-y-3 text-left text-sm">
							<li class="flex items-center text-gray-300">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Custom feature development</span>
							</li>
							<li class="flex items-center text-gray-300">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Dedicated support team</span>
							</li>
							<li class="flex items-center text-gray-300">
								<svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								<span>Flexible pricing options</span>
							</li>
						</ul>
					</div>

					<div class="space-y-3">
						<a href="mailto:<EMAIL>"
							class="block w-full bg-blue-600 text-white py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors text-center">
							Email Sales Team
						</a>
						<button onclick="$crisp.push(['do', 'chat:open'])"
							class="block w-full bg-green-600 text-white py-3 rounded-xl font-semibold hover:bg-green-700 transition-colors text-center">
							Chat with Sales
						</button>
						<div class="text-center text-xs text-gray-400">
							<p>Email: <EMAIL></p>
							<p>Response time: Within 24 hours</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Final CTA -->
	<section class="py-20 bg-gray-900">
		<div class="max-w-4xl mx-auto px-6 text-center">
			<h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
				Ready to Get Started?
			</h2>
			<p class="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
				Join thousands of professionals who trust QRAnalytica for their QR code analytics.
				Start free and upgrade when you're ready.
			</p>

			<div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-8">
				<a href="/tool/qr-code-generator"
					class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
					<span>Create Your First QR Code</span>
					<svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
					</svg>
				</a>
				<!-- <a href="/dashboard"
					class="inline-flex items-center px-8 py-4 bg-transparent text-white font-semibold rounded-xl border-2 border-gray-600 hover:bg-gray-800 transition-all duration-300">
					<span>View Demo Dashboard</span>
				</a> -->
			</div>

			<div class="flex flex-col sm:flex-row gap-8 justify-center items-center text-gray-400 text-sm">
				<div class="flex items-center">
					<svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
					</svg>
					<span>Free to start</span>
				</div>
				<div class="flex items-center">
					<svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
					</svg>
					<span>No credit card required</span>
				</div>
				<div class="flex items-center">
					<svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
					</svg>
					<span>Setup in minutes</span>
				</div>
			</div>
		</div>
	</section>
</div>

<style>
	/* Background Pattern */
	.bg-grid-pattern {
		background-image:
			linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
			linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
		background-size: 20px 20px;
	}

	/* Smooth Transitions */
	.transition-all {
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	/* Gradient Text */
	.bg-clip-text {
		-webkit-background-clip: text;
		background-clip: text;
	}

	/* Hover Effects */
	.group:hover .group-hover\:scale-110 {
		transform: scale(1.1);
	}

	/* Custom Shadows */
	.shadow-xl {
		box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
	}

	/* Backdrop Blur */
	.backdrop-blur-sm {
		backdrop-filter: blur(4px);
	}

	/* Animation for CTA buttons */
	@keyframes pulse {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: .8;
		}
	}

	.animate-pulse {
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}
</style>
